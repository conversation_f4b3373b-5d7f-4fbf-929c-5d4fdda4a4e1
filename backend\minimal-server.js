const http = require('http');
const url = require('url');

const PORT = 3004;

console.log('🔄 Starting minimal HTTP server...');

// In-memory storage for jobs and applications
let jobs = [
  {
    id: '1',
    title: 'Senior React Developer',
    company: 'TechCorp',
    location: 'Remote',
    salary: '$80,000 - $120,000',
    salaryRange: '$80,000 - $120,000',
    experience: '3+ years',
    skills: ['React', 'JavaScript', 'Node.js'],
    description: 'We are looking for an experienced React developer to join our team.',
    requirements: 'React, JavaScript, Node.js, 3+ years experience',
    type: 'Full-time',
    jobType: 'full-time',
    postedDate: new Date().toISOString(),
    recruiter: '<EMAIL>',
    applications: 2
  },
  {
    id: '2',
    title: 'Full Stack Developer',
    company: 'StartupHub',
    location: 'New York, NY',
    salary: '$70,000 - $100,000',
    salaryRange: '$70,000 - $100,000',
    experience: '2+ years',
    skills: ['React', 'Node.js', 'MongoDB'],
    description: 'Join our growing startup as a full stack developer.',
    requirements: 'React, Node.js, MongoDB, 2+ years experience',
    type: 'Full-time',
    jobType: 'full-time',
    postedDate: new Date().toISOString(),
    recruiter: '<EMAIL>',
    applications: 1
  }
];

let applications = [];

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Health check endpoint
  if (path === '/api/health' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({ 
      status: 'OK', 
      message: 'Minimal server is running',
      timestamp: new Date().toISOString()
    }));
    return;
  }

  // Login endpoint
  if (path === '/api/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { email, password } = JSON.parse(body);
        console.log('Login attempt:', email);

        if (email === '<EMAIL>' && password === '987654') {
          res.writeHead(200);
          res.end(JSON.stringify({
            message: 'Login successful',
            token: 'test-token-recruiter',
            user: {
              id: '1',
              email: email,
              userType: 'recruiter',
              fullName: 'Manish Modi',
              companyName: 'The Tech World'
            }
          }));
        } else if (email === '<EMAIL>' && password === '123456') {
          res.writeHead(200);
          res.end(JSON.stringify({
            message: 'Login successful',
            token: 'test-token-jobseeker',
            user: {
              id: '2',
              email: email,
              userType: 'jobseeker',
              fullName: 'Manish Kumar'
            }
          }));
        } else {
          res.writeHead(400);
          res.end(JSON.stringify({ error: 'Invalid credentials' }));
        }
      } catch (error) {
        console.error('Login error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error' }));
      }
    });
    return;
  }

  // Register endpoint
  if (path === '/api/auth/register' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const { email, password, userType, fullName, companyName } = JSON.parse(body);
        console.log('Registration attempt:', email, userType);

        res.writeHead(201);
        res.end(JSON.stringify({
          message: 'User created successfully',
          token: 'test-token-new-user',
          user: {
            id: Date.now().toString(),
            email: email,
            userType: userType,
            fullName: fullName,
            companyName: companyName
          }
        }));
      } catch (error) {
        console.error('Registration error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error' }));
      }
    });
    return;
  }

  // Get all jobs endpoint
  if (path === '/api/jobs' && method === 'GET') {
    console.log('📋 GET /api/jobs - Getting all jobs, count:', jobs.length);

    res.writeHead(200);
    res.end(JSON.stringify(jobs));
    return;
  }

  // Post new job endpoint
  if (path === '/api/jobs' && method === 'POST') {
    console.log('📝 POST /api/jobs - Job creation request received');
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        console.log('📝 Raw request body:', body);
        const jobData = JSON.parse(body);
        console.log('📝 Parsed job data:', JSON.stringify(jobData, null, 2));

        // Validate required fields
        if (!jobData.title || !jobData.company || !jobData.location) {
          console.log('❌ Missing required fields');
          res.writeHead(400);
          res.end(JSON.stringify({
            error: 'Missing required fields: title, company, location'
          }));
          return;
        }

        const newJob = {
          id: Date.now().toString(),
          title: jobData.title,
          company: jobData.company,
          location: jobData.location,
          experience: jobData.experience || 'Not specified',
          skills: Array.isArray(jobData.skills) ? jobData.skills : (jobData.skills ? jobData.skills.split(',').map(s => s.trim()) : []),
          description: jobData.description || 'No description provided',
          salary: jobData.salaryRange || 'Not specified',
          salaryRange: jobData.salaryRange || 'Not specified',
          type: jobData.jobType || 'full-time',
          jobType: jobData.jobType || 'full-time',
          requirements: Array.isArray(jobData.skills) ? jobData.skills.join(', ') : (jobData.skills || 'Not specified'),
          postedDate: new Date().toISOString(),
          recruiter: '<EMAIL>', // Mock recruiter
          applications: 0
        };

        // Add to jobs array
        jobs.push(newJob);
        console.log('✅ Job created and stored successfully:', newJob.title);
        console.log('📊 Total jobs now:', jobs.length);

        res.writeHead(201);
        res.end(JSON.stringify({
          message: 'Job created successfully',
          jobId: newJob.id,
          job: newJob
        }));
      } catch (error) {
        console.error('❌ Job creation error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error: ' + error.message }));
      }
    });
    return;
  }

  // Get recruiter's jobs endpoint
  if (path === '/api/jobs/recruiter/my-jobs' && method === 'GET') {
    console.log('📋 GET /api/jobs/recruiter/my-jobs - Getting recruiter jobs');

    // Filter jobs by current recruiter (mock: <EMAIL>)
    const recruiterJobs = jobs.filter(job => job.recruiter === '<EMAIL>');
    console.log('📊 Found', recruiterJobs.length, 'jobs for recruiter');

    // Add application count to each job
    const jobsWithApplications = recruiterJobs.map(job => ({
      ...job,
      application_count: job.applications || 0
    }));

    res.writeHead(200);
    res.end(JSON.stringify(jobsWithApplications));
    return;
  }

  // Apply for job endpoint
  if (path === '/api/applications' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const applicationData = JSON.parse(body);
        console.log('New job application:', applicationData.jobId);

        const newApplication = {
          id: Date.now().toString(),
          ...applicationData,
          appliedDate: new Date().toISOString(),
          status: 'pending'
        };

        res.writeHead(201);
        res.end(JSON.stringify({
          message: 'Application submitted successfully',
          application: newApplication
        }));
      } catch (error) {
        console.error('Application error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error' }));
      }
    });
    return;
  }

  // Get user's applications endpoint
  if (path === '/api/applications/my-applications' && method === 'GET') {
    console.log('Getting user applications');

    const mockApplications = [
      {
        id: '1',
        jobId: '1',
        jobTitle: 'Senior React Developer',
        company: 'TechCorp',
        appliedDate: new Date().toISOString(),
        status: 'pending'
      }
    ];

    res.writeHead(200);
    res.end(JSON.stringify(mockApplications));
    return;
  }

  // Get recruiter's applications endpoint
  if (path === '/api/applications/recruiter/all' && method === 'GET') {
    console.log('Getting recruiter applications');

    const mockRecruiterApplications = [
      {
        id: '1',
        jobId: '1',
        jobTitle: 'Senior React Developer',
        applicantName: 'John Doe',
        applicantEmail: '<EMAIL>',
        appliedDate: new Date().toISOString(),
        status: 'pending',
        resume: 'resume-123.pdf'
      }
    ];

    res.writeHead(200);
    res.end(JSON.stringify(mockRecruiterApplications));
    return;
  }

  // Get user profile endpoint
  if (path === '/api/users/profile' && method === 'GET') {
    console.log('Getting user profile');

    const mockProfile = {
      id: '1',
      email: '<EMAIL>',
      fullName: 'Manish Modi',
      userType: 'recruiter',
      companyName: 'The Tech World'
    };

    res.writeHead(200);
    res.end(JSON.stringify(mockProfile));
    return;
  }

  // Update user profile endpoint
  if (path === '/api/users/profile' && method === 'PUT') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const profileData = JSON.parse(body);
        console.log('Updating user profile:', profileData.fullName);

        res.writeHead(200);
        res.end(JSON.stringify({
          message: 'Profile updated successfully',
          user: profileData
        }));
      } catch (error) {
        console.error('Profile update error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error' }));
      }
    });
    return;
  }

  // 404 for other routes
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Route not found' }));
});

server.listen(PORT, () => {
  console.log(`✅ Minimal server running on http://localhost:${PORT}`);
  console.log(`✅ Health: http://localhost:${PORT}/api/health`);
  console.log(`✅ Ready to accept connections`);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔄 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

console.log('✅ Server setup complete');
