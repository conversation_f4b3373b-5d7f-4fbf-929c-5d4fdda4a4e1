const http = require('http');
const url = require('url');

const PORT = 3004;

console.log('🔄 Starting minimal HTTP server...');

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Health check endpoint
  if (path === '/api/health' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({ 
      status: 'OK', 
      message: 'Minimal server is running',
      timestamp: new Date().toISOString()
    }));
    return;
  }

  // Login endpoint
  if (path === '/api/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { email, password } = JSON.parse(body);
        console.log('Login attempt:', email);

        if (email === '<EMAIL>' && password === '987654') {
          res.writeHead(200);
          res.end(JSON.stringify({
            message: 'Login successful',
            token: 'test-token-recruiter',
            user: {
              id: '1',
              email: email,
              userType: 'recruiter',
              fullName: 'Manish Modi',
              companyName: 'The Tech World'
            }
          }));
        } else if (email === '<EMAIL>' && password === '123456') {
          res.writeHead(200);
          res.end(JSON.stringify({
            message: 'Login successful',
            token: 'test-token-jobseeker',
            user: {
              id: '2',
              email: email,
              userType: 'jobseeker',
              fullName: 'Manish Kumar'
            }
          }));
        } else {
          res.writeHead(400);
          res.end(JSON.stringify({ error: 'Invalid credentials' }));
        }
      } catch (error) {
        console.error('Login error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error' }));
      }
    });
    return;
  }

  // Register endpoint
  if (path === '/api/auth/register' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { email, password, userType, fullName, companyName } = JSON.parse(body);
        console.log('Registration attempt:', email, userType);

        res.writeHead(201);
        res.end(JSON.stringify({
          message: 'User created successfully',
          token: 'test-token-new-user',
          user: {
            id: Date.now().toString(),
            email: email,
            userType: userType,
            fullName: fullName,
            companyName: companyName
          }
        }));
      } catch (error) {
        console.error('Registration error:', error);
        res.writeHead(500);
        res.end(JSON.stringify({ error: 'Server error' }));
      }
    });
    return;
  }

  // 404 for other routes
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Route not found' }));
});

server.listen(PORT, () => {
  console.log(`✅ Minimal server running on http://localhost:${PORT}`);
  console.log(`✅ Health: http://localhost:${PORT}/api/health`);
  console.log(`✅ Ready to accept connections`);
});

server.on('error', (err) => {
  console.error('❌ Server error:', err);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🔄 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

console.log('✅ Server setup complete');
